import { Controller, Get, Query, Param, Post, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiQuery, ApiParam, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { MonitoringService, MonitoringMetrics, Alert } from './monitoring.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@ApiTags('监控')
@Controller('monitoring')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class MonitoringController {
  constructor(private readonly monitoringService: MonitoringService) {}

  @Get('metrics')
  @ApiOperation({ summary: '获取监控指标' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: '返回的指标数量限制' })
  @Roles('admin', 'operator')
  getMetrics(@Query('limit') limit?: number): MonitoringMetrics[] {
    return this.monitoringService.getMetrics(limit ? parseInt(limit as unknown as string) : undefined);
  }

  @Get('metrics/latest')
  @ApiOperation({ summary: '获取最新监控指标' })
  @Roles('admin', 'operator', 'user')
  getLatestMetrics(): MonitoringMetrics | null {
    return this.monitoringService.getLatestMetrics();
  }

  @Get('alerts')
  @ApiOperation({ summary: '获取告警' })
  @ApiQuery({ name: 'onlyUnresolved', required: false, type: Boolean, description: '是否只返回未解决的告警' })
  @Roles('admin', 'operator')
  getAlerts(@Query('onlyUnresolved') onlyUnresolved?: string): Alert[] {
    return this.monitoringService.getAlerts(onlyUnresolved === 'true');
  }

  @Get('alerts/:id')
  @ApiOperation({ summary: '获取特定告警' })
  @ApiParam({ name: 'id', required: true, description: '告警ID' })
  @Roles('admin', 'operator')
  getAlert(@Param('id') id: string): Alert | undefined {
    const alerts = this.monitoringService.getAlerts();
    return alerts.find(alert => alert.id === id);
  }

  @Post('alerts/:id/resolve')
  @ApiOperation({ summary: '手动解决告警' })
  @ApiParam({ name: 'id', required: true, description: '告警ID' })
  @Roles('admin', 'operator')
  resolveAlert(@Param('id') id: string): { success: boolean; message: string } {
    const alerts = this.monitoringService.getAlerts();
    const alert = alerts.find(alert => alert.id === id && !alert.resolved);
    
    if (!alert) {
      return { success: false, message: '告警不存在或已解决' };
    }
    
    alert.resolved = true;
    alert.resolvedAt = Date.now();
    
    return { success: true, message: '告警已手动解决' };
  }

  @Get('health')
  @ApiOperation({ summary: '获取健康状态' })
  @Roles('admin', 'operator', 'user')
  getHealth(): {
    status: 'UP' | 'DOWN' | 'DEGRADED';
    details: {
      cpu: { status: 'UP' | 'DOWN'; usage: number };
      memory: { status: 'UP' | 'DOWN'; usage: number };
      services: { status: 'UP' | 'DOWN'; count: number; healthy: number; unhealthy: number };
      cache: { status: 'UP' | 'DOWN'; hitRate: number; size: number };
    };
  } {
    const metrics = this.monitoringService.getLatestMetrics();
    
    if (!metrics) {
      return {
        status: 'DOWN',
        details: {
          cpu: { status: 'DOWN', usage: 0 },
          memory: { status: 'DOWN', usage: 0 },
          services: { status: 'DOWN', count: 0, healthy: 0, unhealthy: 0 },
          cache: { status: 'DOWN', hitRate: 0, size: 0 },
        },
      };
    }
    
    const cpuStatus = metrics.cpuUsage < 0.8 ? 'UP' : 'DOWN';
    const memoryStatus = metrics.memoryUsage < 0.8 ? 'UP' : 'DOWN';
    const servicesStatus = metrics.unhealthyInstanceCount / (metrics.instanceCount || 1) < 0.2 ? 'UP' : 'DOWN';
    const cacheStatus = metrics.cacheStats.hitRate > 0.5 ? 'UP' : 'DOWN';
    
    const overallStatus = 
      cpuStatus === 'DOWN' || memoryStatus === 'DOWN' ? 'DOWN' :
      servicesStatus === 'DOWN' || cacheStatus === 'DOWN' ? 'DEGRADED' : 'UP';
    
    return {
      status: overallStatus,
      details: {
        cpu: { status: cpuStatus, usage: metrics.cpuUsage },
        memory: { status: memoryStatus, usage: metrics.memoryUsage },
        services: {
          status: servicesStatus,
          count: metrics.serviceCount,
          healthy: metrics.healthyInstanceCount,
          unhealthy: metrics.unhealthyInstanceCount,
        },
        cache: {
          status: cacheStatus,
          hitRate: metrics.cacheStats.hitRate,
          size: metrics.cacheStats.size,
        },
      },
    };
  }
}
