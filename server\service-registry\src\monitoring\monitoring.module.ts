import { Module } from '@nestjs/common';
import { MonitoringService } from './monitoring.service';
import { MonitoringController } from './monitoring.controller';
import { RegistryModule } from '../registry/registry.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [RegistryModule, AuthModule],
  controllers: [MonitoringController],
  providers: [MonitoringService],
  exports: [MonitoringService],
})
export class MonitoringModule {}
