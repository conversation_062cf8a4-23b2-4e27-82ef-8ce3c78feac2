/**
 * 服务实例实体
 */
import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { ServiceEntity } from './service.entity';
import { HealthCheckConfig } from '../health-check/health-check.interface';

@Entity('service_instances')
export class ServiceInstanceEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  instanceId: string;

  @Column()
  host: string;

  @Column({ nullable: true })
  ipAddress: string;

  @Column()
  port: number;

  @Column({ nullable: true })
  httpPort: number;

  @Column({ default: true })
  isHealthy: boolean;

  /**
   * 服务实例状态
   */
  @Column({ default: 'STARTING' })
  status: string;

  /**
   * 服务实例权重
   * 用于负载均衡算法
   */
  @Column({ default: 100 })
  weight: number;

  /**
   * 服务实例区域/可用区
   * 用于区域感知路由
   */
  @Column({ nullable: true })
  zone: string;

  /**
   * 健康检查URL
   */
  @Column({ nullable: true })
  healthCheckUrl: string;

  /**
   * 健康检查配置
   */
  @Column({ type: 'json', nullable: true })
  healthCheckConfig: HealthCheckConfig;

  /**
   * 服务实例标签
   */
  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'timestamp', nullable: true })
  lastHeartbeat: Date;

  @Column({ type: 'timestamp', nullable: true })
  lastUpdated: Date;

  @ManyToOne(() => ServiceEntity, service => service.instances, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'serviceId' })
  service: ServiceEntity;

  @Column()
  serviceId: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
