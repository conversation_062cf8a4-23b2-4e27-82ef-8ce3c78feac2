/**
 * 服务注册中心入口文件
 */
import { NestFactory } from '@nestjs/core';
import { Transport } from '@nestjs/microservices';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import * as compression from 'compression';
import helmet from 'helmet';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';

async function bootstrap() {
  // 创建Nest应用实例
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  
  // 配置微服务
  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      host: configService.get<string>('SERVICE_REGISTRY_HOST', 'localhost'),
      port: configService.get<number>('SERVICE_REGISTRY_PORT', 3010),
    },
  });
  
  // 配置HTTP服务
  // 全局前缀
  app.setGlobalPrefix('api');
  
  // 全局管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );
  
  // 启用CORS
  app.enableCors();
  
  // 启用压缩
  app.use(compression());
  
  // 启用安全头
  app.use(helmet());
  
  // Swagger文档
  const config = new DocumentBuilder()
    .setTitle('服务注册中心API')
    .setDescription('DL（Digital Learning）引擎服务注册中心API文档')
    .setVersion('1.0')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);
  
  // 启动微服务
  await app.startAllMicroservices();
  
  // 启动HTTP服务
  const httpPort = configService.get<number>('SERVICE_REGISTRY_HTTP_PORT', 4010);
  await app.listen(httpPort);
  console.log(`服务注册中心已启动，微服务端口: ${configService.get<number>('SERVICE_REGISTRY_PORT', 3010)}, HTTP端口: ${httpPort}`);
}

bootstrap();
