import { Injectable, Logger, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { Redis } from 'ioredis';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { v4 as uuidv4 } from 'uuid';
import { EVENT_BUS_OPTIONS } from './event-bus.constants';
import { Event, EventBusOptions, EventHandler } from './event-bus.interface';

/**
 * 事件优先级
 */
export enum EventPriority {
  LOW = 0,
  MEDIUM = 1,
  HIGH = 2,
  CRITICAL = 3,
}

/**
 * 增强的事件接口
 */
export interface EnhancedEvent extends Event {
  /**
   * 事件优先级
   */
  priority?: EventPriority;
  
  /**
   * 事件过期时间
   */
  expiresAt?: number;
  
  /**
   * 事件重试次数
   */
  retryCount?: number;
  
  /**
   * 事件标签
   */
  tags?: string[];
  
  /**
   * 事件元数据
   */
  metadata?: Record<string, any>;
}

/**
 * 事件订阅选项
 */
export interface EventSubscriptionOptions {
  /**
   * 是否只处理一次
   */
  once?: boolean;
  
  /**
   * 事件过滤器
   */
  filter?: (event: EnhancedEvent) => boolean;
  
  /**
   * 最大重试次数
   */
  maxRetries?: number;
  
  /**
   * 重试延迟（毫秒）
   */
  retryDelay?: number;
  
  /**
   * 超时时间（毫秒）
   */
  timeout?: number;
  
  /**
   * 批处理大小
   */
  batchSize?: number;
  
  /**
   * 批处理间隔（毫秒）
   */
  batchInterval?: number;
}

/**
 * 增强的事件总线服务
 */
@Injectable()
export class EnhancedEventBusService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(EnhancedEventBusService.name);
  private readonly localHandlers = new Map<string, Set<{
    handler: Function;
    options: EventSubscriptionOptions;
  }>>();
  private readonly channelPrefix: string;
  private readonly serviceName: string;
  private readonly eventHistory = new Map<string, EnhancedEvent[]>();
  private readonly maxHistorySize: number;
  private readonly eventBatches = new Map<string, {
    events: EnhancedEvent[];
    timer: NodeJS.Timeout;
  }>();
  private readonly pendingEvents = new Set<string>();
  private readonly failedEvents = new Map<string, {
    event: EnhancedEvent;
    error: Error;
    timestamp: number;
  }>();
  private subscriber: Redis;
  private publisher: Redis;
  private isInitialized = false;

  constructor(
    @Inject(EVENT_BUS_OPTIONS) private readonly options: EventBusOptions,
    @InjectRedis() private readonly redis: Redis,
  ) {
    this.channelPrefix = options.channelPrefix || 'dl-engine:events:';
    this.serviceName = options.serviceName;
    this.maxHistorySize = options.maxHistorySize || 100;
  }

  /**
   * 模块初始化
   */
  async onModuleInit() {
    if (this.options.redis) {
      // 创建订阅者和发布者客户端
      this.subscriber = this.redis;
      this.publisher = this.redis.duplicate();
      
      // 订阅所有事件通道
      await this.subscriber.subscribe(`${this.channelPrefix}*`);
      
      // 处理接收到的消息
      this.subscriber.on('message', (channel, message) => {
        try {
          const event = JSON.parse(message) as EnhancedEvent;
          const eventName = channel.replace(this.channelPrefix, '');
          
          // 不处理自己发布的事件
          if (event.publisher === this.serviceName) {
            return;
          }
          
          this.logger.debug(`收到事件: ${eventName}, 发布者: ${event.publisher}`);
          
          // 检查事件是否过期
          if (event.expiresAt && event.expiresAt < Date.now()) {
            this.logger.debug(`事件已过期: ${eventName}, ID: ${event.id}`);
            return;
          }
          
          // 处理事件
          this.processLocalHandlers(eventName, event);
          
          // 添加到历史记录
          this.addToHistory(eventName, event);
        } catch (error) {
          this.logger.error(`处理事件消息失败: ${error.message}`, error.stack);
        }
      });
      
      this.logger.log(`增强事件总线已初始化，服务名称: ${this.serviceName}`);
      this.isInitialized = true;
    } else if (this.options.enableLocalEvents) {
      this.logger.log(`增强事件总线已初始化（仅本地模式），服务名称: ${this.serviceName}`);
      this.isInitialized = true;
    } else {
      this.logger.warn('增强事件总线未初始化，未配置Redis且未启用本地事件');
    }
  }

  /**
   * 模块销毁
   */
  async onModuleDestroy() {
    // 清理批处理定时器
    for (const { timer } of this.eventBatches.values()) {
      clearTimeout(timer);
    }
    
    if (this.subscriber) {
      await this.subscriber.unsubscribe();
      await this.subscriber.quit();
    }
    
    if (this.publisher) {
      await this.publisher.quit();
    }
    
    this.logger.log('增强事件总线已关闭');
  }

  /**
   * 发布事件
   * @param eventName 事件名称
   * @param data 事件数据
   * @param options 事件选项
   */
  async publish<T = any>(
    eventName: string,
    data: T,
    options: {
      correlationId?: string;
      causationId?: string;
      priority?: EventPriority;
      expiresIn?: number;
      tags?: string[];
      metadata?: Record<string, any>;
    } = {},
  ): Promise<string> {
    const eventId = uuidv4();
    const event: EnhancedEvent = {
      name: eventName,
      data,
      timestamp: Date.now(),
      publisher: this.serviceName,
      id: eventId,
      version: 1,
      correlationId: options.correlationId,
      causationId: options.causationId,
      priority: options.priority || EventPriority.MEDIUM,
      expiresAt: options.expiresIn ? Date.now() + options.expiresIn : undefined,
      retryCount: 0,
      tags: options.tags,
      metadata: options.metadata,
    };

    // 处理本地事件
    if (this.options.enableLocalEvents !== false) {
      this.processLocalHandlers(eventName, event);
    }

    // 发布到Redis
    if (this.publisher) {
      try {
        const channel = `${this.channelPrefix}${eventName}`;
        await this.publisher.publish(channel, JSON.stringify(event));
        this.logger.debug(`已发布事件: ${eventName}, ID: ${eventId}`);
        
        // 添加到历史记录
        this.addToHistory(eventName, event);
      } catch (error) {
        this.logger.error(`发布事件失败: ${error.message}`, error.stack);
        
        // 记录失败的事件
        this.failedEvents.set(eventId, {
          event,
          error: error as Error,
          timestamp: Date.now(),
        });
        
        throw error;
      }
    }
    
    return eventId;
  }

  /**
   * 批量发布事件
   * @param events 事件列表
   */
  async publishBatch(events: {
    name: string;
    data: any;
    options?: {
      correlationId?: string;
      causationId?: string;
      priority?: EventPriority;
      expiresIn?: number;
      tags?: string[];
      metadata?: Record<string, any>;
    };
  }[]): Promise<string[]> {
    const eventIds: string[] = [];
    
    // 使用管道批量发布
    if (this.publisher) {
      const pipeline = this.publisher.pipeline();
      
      for (const { name, data, options } of events) {
        const eventId = uuidv4();
        eventIds.push(eventId);
        
        const event: EnhancedEvent = {
          name,
          data,
          timestamp: Date.now(),
          publisher: this.serviceName,
          id: eventId,
          version: 1,
          correlationId: options?.correlationId,
          causationId: options?.causationId,
          priority: options?.priority || EventPriority.MEDIUM,
          expiresAt: options?.expiresIn ? Date.now() + options.expiresIn : undefined,
          retryCount: 0,
          tags: options?.tags,
          metadata: options?.metadata,
        };
        
        // 处理本地事件
        if (this.options.enableLocalEvents !== false) {
          this.processLocalHandlers(name, event);
        }
        
        // 添加到管道
        const channel = `${this.channelPrefix}${name}`;
        pipeline.publish(channel, JSON.stringify(event));
        
        // 添加到历史记录
        this.addToHistory(name, event);
      }
      
      try {
        await pipeline.exec();
        this.logger.debug(`已批量发布 ${events.length} 个事件`);
      } catch (error) {
        this.logger.error(`批量发布事件失败: ${error.message}`, error.stack);
        throw error;
      }
    } else {
      // 如果没有Redis，则逐个发布
      for (const { name, data, options } of events) {
        const eventId = await this.publish(name, data, options);
        eventIds.push(eventId);
      }
    }
    
    return eventIds;
  }

  /**
   * 延迟发布事件
   * @param eventName 事件名称
   * @param data 事件数据
   * @param delay 延迟时间（毫秒）
   * @param options 事件选项
   */
  async publishDelayed<T = any>(
    eventName: string,
    data: T,
    delay: number,
    options: {
      correlationId?: string;
      causationId?: string;
      priority?: EventPriority;
      expiresIn?: number;
      tags?: string[];
      metadata?: Record<string, any>;
    } = {},
  ): Promise<string> {
    const eventId = uuidv4();
    
    // 创建定时器
    setTimeout(async () => {
      try {
        await this.publish(eventName, data, options);
      } catch (error) {
        this.logger.error(`延迟发布事件失败: ${error.message}`, error.stack);
      }
    }, delay);
    
    return eventId;
  }

  /**
   * 订阅事件
   * @param eventName 事件名称
   * @param handler 事件处理函数
   * @param options 订阅选项
   */
  subscribe<T extends EnhancedEvent = EnhancedEvent>(
    eventName: string,
    handler: (event: T) => Promise<void>,
    options: EventSubscriptionOptions = {},
  ): () => void {
    if (!this.localHandlers.has(eventName)) {
      this.localHandlers.set(eventName, new Set());
    }
    
    this.localHandlers.get(eventName).add({ handler, options });
    this.logger.debug(`已订阅事件: ${eventName}`);
    
    // 返回取消订阅函数
    return () => {
      if (this.localHandlers.has(eventName)) {
        this.localHandlers.get(eventName).forEach((h) => {
          if (h.handler === handler) {
            this.localHandlers.get(eventName).delete(h);
          }
        });
        
        if (this.localHandlers.get(eventName).size === 0) {
          this.localHandlers.delete(eventName);
        }
      }
    };
  }

  /**
   * 批量订阅事件
   * @param eventNames 事件名称列表
   * @param handler 事件处理函数
   * @param options 订阅选项
   */
  subscribeBatch<T extends EnhancedEvent = EnhancedEvent>(
    eventNames: string[],
    handler: (event: T) => Promise<void>,
    options: EventSubscriptionOptions = {},
  ): () => void {
    const unsubscribers: (() => void)[] = [];
    
    for (const eventName of eventNames) {
      const unsubscribe = this.subscribe(eventName, handler, options);
      unsubscribers.push(unsubscribe);
    }
    
    // 返回批量取消订阅函数
    return () => {
      for (const unsubscribe of unsubscribers) {
        unsubscribe();
      }
    };
  }

  /**
   * 处理本地事件处理器
   * @param eventName 事件名称
   * @param event 事件对象
   */
  private async processLocalHandlers(eventName: string, event: EnhancedEvent): Promise<void> {
    const handlers = this.localHandlers.get(eventName);
    
    if (!handlers || handlers.size === 0) {
      return;
    }
    
    // 标记事件为处理中
    this.pendingEvents.add(event.id);
    
    // 检查是否需要批处理
    for (const { handler, options } of handlers) {
      if (options.batchSize && options.batchSize > 1) {
        // 批处理模式
        await this.handleBatchEvent(eventName, event, handler, options);
      } else {
        // 单事件处理模式
        await this.handleSingleEvent(event, handler, options);
      }
    }
    
    // 移除处理中标记
    this.pendingEvents.delete(event.id);
  }

  /**
   * 处理单个事件
   * @param event 事件对象
   * @param handler 处理函数
   * @param options 订阅选项
   */
  private async handleSingleEvent(
    event: EnhancedEvent,
    handler: Function,
    options: EventSubscriptionOptions,
  ): Promise<void> {
    // 应用过滤器
    if (options.filter && !options.filter(event)) {
      return;
    }
    
    try {
      // 设置超时
      let timeoutId: NodeJS.Timeout | null = null;
      
      if (options.timeout) {
        const timeoutPromise = new Promise<void>((_, reject) => {
          timeoutId = setTimeout(() => {
            reject(new Error(`处理事件超时: ${event.name}, ID: ${event.id}`));
          }, options.timeout);
        });
        
        // 使用Promise.race实现超时
        await Promise.race([
          handler(event),
          timeoutPromise,
        ]);
        
        // 清除超时定时器
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
      } else {
        // 无超时处理
        await handler(event);
      }
      
      // 如果是一次性处理器，则移除
      if (options.once) {
        this.localHandlers.get(event.name)?.forEach((h) => {
          if (h.handler === handler) {
            this.localHandlers.get(event.name).delete(h);
          }
        });
        
        if (this.localHandlers.get(event.name)?.size === 0) {
          this.localHandlers.delete(event.name);
        }
      }
    } catch (error) {
      this.logger.error(`处理事件失败: ${error.message}`, (error as Error).stack);
      
      // 重试逻辑
      if (options.maxRetries && event.retryCount < options.maxRetries) {
        event.retryCount = (event.retryCount || 0) + 1;
        
        const delay = options.retryDelay || 1000 * Math.pow(2, event.retryCount); // 指数退避
        
        this.logger.debug(`将在 ${delay}ms 后重试事件: ${event.name}, ID: ${event.id}, 重试次数: ${event.retryCount}`);
        
        setTimeout(() => {
          this.handleSingleEvent(event, handler, options).catch((retryError) => {
            this.logger.error(`重试事件失败: ${retryError.message}`, retryError.stack);
          });
        }, delay);
      } else {
        // 记录失败的事件
        this.failedEvents.set(event.id, {
          event,
          error: error as Error,
          timestamp: Date.now(),
        });
      }
    }
  }

  /**
   * 处理批处理事件
   * @param eventName 事件名称
   * @param event 事件对象
   * @param handler 处理函数
   * @param options 订阅选项
   */
  private async handleBatchEvent(
    eventName: string,
    event: EnhancedEvent,
    handler: Function,
    options: EventSubscriptionOptions,
  ): Promise<void> {
    // 应用过滤器
    if (options.filter && !options.filter(event)) {
      return;
    }
    
    const batchKey = `${eventName}:${handler.toString()}`;
    
    if (!this.eventBatches.has(batchKey)) {
      // 创建新的批处理
      const batchInterval = options.batchInterval || 1000;
      
      const timer = setTimeout(() => {
        this.processBatch(batchKey, handler, options);
      }, batchInterval);
      
      this.eventBatches.set(batchKey, {
        events: [event],
        timer,
      });
    } else {
      // 添加到现有批处理
      const batch = this.eventBatches.get(batchKey);
      batch.events.push(event);
      
      // 如果达到批处理大小，立即处理
      if (batch.events.length >= (options.batchSize || 10)) {
        clearTimeout(batch.timer);
        this.processBatch(batchKey, handler, options);
      }
    }
  }

  /**
   * 处理批处理
   * @param batchKey 批处理键
   * @param handler 处理函数
   * @param options 订阅选项
   */
  private async processBatch(
    batchKey: string,
    handler: Function,
    options: EventSubscriptionOptions,
  ): Promise<void> {
    if (!this.eventBatches.has(batchKey)) {
      return;
    }
    
    const { events } = this.eventBatches.get(batchKey);
    this.eventBatches.delete(batchKey);
    
    if (events.length === 0) {
      return;
    }
    
    try {
      await handler(events);
      
      // 如果是一次性处理器，则移除
      if (options.once) {
        const eventName = events[0].name;
        this.localHandlers.get(eventName)?.forEach((h) => {
          if (h.handler === handler) {
            this.localHandlers.get(eventName).delete(h);
          }
        });
        
        if (this.localHandlers.get(eventName)?.size === 0) {
          this.localHandlers.delete(eventName);
        }
      }
    } catch (error) {
      this.logger.error(`处理批处理事件失败: ${error.message}`, (error as Error).stack);
      
      // 对于批处理失败，我们将每个事件单独处理
      for (const event of events) {
        this.handleSingleEvent(event, handler, options).catch((singleError) => {
          this.logger.error(`单独处理批处理事件失败: ${singleError.message}`, singleError.stack);
        });
      }
    }
  }

  /**
   * 添加事件到历史记录
   * @param eventName 事件名称
   * @param event 事件对象
   */
  private addToHistory(eventName: string, event: EnhancedEvent): void {
    if (!this.eventHistory.has(eventName)) {
      this.eventHistory.set(eventName, []);
    }
    
    const history = this.eventHistory.get(eventName);
    history.push(event);
    
    // 限制历史记录大小
    if (history.length > this.maxHistorySize) {
      history.shift();
    }
  }

  /**
   * 获取事件历史记录
   * @param eventName 事件名称
   * @param limit 限制数量
   */
  getEventHistory(eventName: string, limit: number = this.maxHistorySize): EnhancedEvent[] {
    if (!this.eventHistory.has(eventName)) {
      return [];
    }
    
    const history = this.eventHistory.get(eventName);
    return history.slice(-limit);
  }

  /**
   * 获取失败的事件
   * @param limit 限制数量
   */
  getFailedEvents(limit: number = 100): {
    event: EnhancedEvent;
    error: Error;
    timestamp: number;
  }[] {
    const events = Array.from(this.failedEvents.values());
    events.sort((a, b) => b.timestamp - a.timestamp);
    return events.slice(0, limit);
  }

  /**
   * 重试失败的事件
   * @param eventId 事件ID
   */
  async retryFailedEvent(eventId: string): Promise<boolean> {
    if (!this.failedEvents.has(eventId)) {
      return false;
    }
    
    const { event } = this.failedEvents.get(eventId);
    
    try {
      await this.publish(event.name, event.data, {
        correlationId: event.correlationId,
        causationId: event.causationId,
        priority: event.priority,
        expiresIn: event.expiresAt ? event.expiresAt - Date.now() : undefined,
        tags: event.tags,
        metadata: event.metadata,
      });
      
      // 移除失败记录
      this.failedEvents.delete(eventId);
      
      return true;
    } catch (error) {
      this.logger.error(`重试失败事件失败: ${error.message}`, (error as Error).stack);
      return false;
    }
  }

  /**
   * 获取待处理的事件数量
   */
  getPendingEventsCount(): number {
    return this.pendingEvents.size;
  }

  /**
   * 获取事件总线状态
   */
  getStatus(): {
    isInitialized: boolean;
    pendingEvents: number;
    failedEvents: number;
    subscribedEvents: string[];
    batchesCount: number;
  } {
    return {
      isInitialized: this.isInitialized,
      pendingEvents: this.pendingEvents.size,
      failedEvents: this.failedEvents.size,
      subscribedEvents: Array.from(this.localHandlers.keys()),
      batchesCount: this.eventBatches.size,
    };
  }
}
