import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ClientProxy, ClientProxyFactory, Transport } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

/**
 * JWT认证守卫
 * 验证请求中的JWT令牌
 */
@Injectable()
export class JwtAuthGuard implements CanActivate {
  private userService: ClientProxy;

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {
    // 创建用户服务客户端
    this.userService = ClientProxyFactory.create({
      transport: Transport.TCP,
      options: {
        host: this.configService.get<string>('USER_SERVICE_HOST', 'localhost'),
        port: this.configService.get<number>('USER_SERVICE_PORT', 3001),
      },
    });
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    if (!authHeader) {
      throw new UnauthorizedException('缺少认证令牌');
    }

    const [type, token] = authHeader.split(' ');

    if (type !== 'Bearer') {
      throw new UnauthorizedException('无效的认证类型');
    }

    try {
      // 验证JWT
      const payload = this.jwtService.verify(token);

      // 从用户服务获取用户信息
      const user = await firstValueFrom(
        this.userService.send({ cmd: 'validateJwt' }, payload),
      );

      // 将用户信息添加到请求中
      request.user = user;

      return true;
    } catch (error) {
      throw new UnauthorizedException('无效的认证令牌');
    }
  }
}
