/**
 * 区域感知负载均衡策略
 */
import { Injectable } from '@nestjs/common';
import { BaseLoadBalancerStrategy } from './base-load-balancer.strategy';
import { LoadBalancerContext, LoadBalancerAlgorithm } from './load-balancer.interface';
import { ServiceInstanceEntity } from '../entities/service-instance.entity';

/**
 * 区域感知配置
 */
interface ZoneAwareConfig {
  /** 区域亲和性权重 (0-1) */
  zoneAffinityWeight: number;
  /** 区域故障转移阈值 */
  failoverThreshold: number;
  /** 跨区域延迟矩阵 */
  crossZoneLatencyMatrix?: Record<string, Record<string, number>>;
  /** 区域优先级 */
  zonePriorities?: Record<string, number>;
  /** 是否启用区域故障转移 */
  enableZoneFailover: boolean;
  /** 是否启用延迟感知 */
  enableLatencyAwareness: boolean;
  /** 是否启用区域负载均衡 */
  enableZoneLoadBalancing: boolean;
}

/**
 * 区域统计信息
 */
interface ZoneStats {
  /** 区域名称 */
  zone: string;
  /** 实例数量 */
  instanceCount: number;
  /** 健康实例数量 */
  healthyInstanceCount: number;
  /** 平均负载 */
  averageLoad: number;
  /** 最后更新时间 */
  lastUpdated: number;
}

/**
 * 区域感知负载均衡策略
 */
@Injectable()
export class ZoneAwareLoadBalancerStrategy extends BaseLoadBalancerStrategy {
  
  // 区域统计信息
  private readonly zoneStats = new Map<string, Map<string, ZoneStats>>();
  
  // 默认配置
  private readonly defaultConfig: ZoneAwareConfig = {
    zoneAffinityWeight: 0.8,
    failoverThreshold: 0.7,
    enableZoneFailover: true,
    enableLatencyAwareness: true,
    enableZoneLoadBalancing: true,
  };
  
  constructor() {
    super(LoadBalancerAlgorithm.ZONE_AWARE);
    this.config.algorithm = LoadBalancerAlgorithm.ZONE_AWARE;
    this.config.enableZoneAffinity = true;
    this.config.zoneAffinityWeight = this.defaultConfig.zoneAffinityWeight;
    this.config.options = this.defaultConfig;
  }
  
  /**
   * 使用区域感知算法选择服务实例
   * @param instances 服务实例列表
   * @param context 负载均衡上下文
   */
  protected async doSelect(
    instances: ServiceInstanceEntity[],
    context: LoadBalancerContext,
  ): Promise<ServiceInstanceEntity | null> {
    if (!instances || instances.length === 0) {
      return null;
    }
    
    // 更新区域统计信息
    this.updateZoneStats(context.serviceName, instances);
    
    // 获取客户端区域
    const clientZone = context.clientZone;
    
    if (!clientZone) {
      // 如果没有客户端区域信息，则使用随机策略
      return instances[Math.floor(Math.random() * instances.length)];
    }
    
    // 按区域分组实例
    const instancesByZone = this.groupInstancesByZone(instances);
    
    // 获取配置
    const config = this.getZoneAwareConfig();
    
    // 检查客户端区域是否有可用实例
    const sameZoneInstances = instancesByZone.get(clientZone) || [];
    
    if (sameZoneInstances.length > 0) {
      // 检查是否需要区域故障转移
      if (config.enableZoneFailover && this.shouldFailover(context.serviceName, clientZone)) {
        this.logger.debug(`区域 ${clientZone} 需要故障转移，寻找其他区域的实例`);
        return this.selectInstanceWithFailover(instances, instancesByZone, clientZone, context);
      }
      
      // 使用区域内负载均衡
      if (Math.random() < config.zoneAffinityWeight) {
        return this.selectInstanceInZone(sameZoneInstances, context);
      }
    }
    
    // 跨区域选择
    return this.selectInstanceAcrossZones(instances, instancesByZone, clientZone, context);
  }
  
  /**
   * 在区域内选择实例
   * @param instances 区域内实例列表
   * @param context 负载均衡上下文
   */
  private selectInstanceInZone(
    instances: ServiceInstanceEntity[],
    context: LoadBalancerContext,
  ): ServiceInstanceEntity {
    // 简单随机选择，可以替换为其他策略
    return instances[Math.floor(Math.random() * instances.length)];
  }
  
  /**
   * 跨区域选择实例
   * @param allInstances 所有实例
   * @param instancesByZone 按区域分组的实例
   * @param clientZone 客户端区域
   * @param context 负载均衡上下文
   */
  private selectInstanceAcrossZones(
    allInstances: ServiceInstanceEntity[],
    instancesByZone: Map<string, ServiceInstanceEntity[]>,
    clientZone: string,
    context: LoadBalancerContext,
  ): ServiceInstanceEntity {
    const config = this.getZoneAwareConfig();
    
    // 如果启用延迟感知，则根据延迟选择区域
    if (config.enableLatencyAwareness && config.crossZoneLatencyMatrix) {
      const latencyMatrix = config.crossZoneLatencyMatrix[clientZone];
      
      if (latencyMatrix) {
        // 按延迟排序区域
        const zones = Array.from(instancesByZone.keys())
          .filter(zone => zone !== clientZone && instancesByZone.get(zone).length > 0)
          .sort((a, b) => (latencyMatrix[a] || 100) - (latencyMatrix[b] || 100));
        
        if (zones.length > 0) {
          // 选择延迟最低的区域
          const targetZone = zones[0];
          return this.selectInstanceInZone(instancesByZone.get(targetZone), context);
        }
      }
    }
    
    // 如果启用区域优先级，则根据优先级选择区域
    if (config.zonePriorities) {
      const zones = Array.from(instancesByZone.keys())
        .filter(zone => zone !== clientZone && instancesByZone.get(zone).length > 0)
        .sort((a, b) => (config.zonePriorities[a] || 0) - (config.zonePriorities[b] || 0));
      
      if (zones.length > 0) {
        // 选择优先级最高的区域
        const targetZone = zones[0];
        return this.selectInstanceInZone(instancesByZone.get(targetZone), context);
      }
    }
    
    // 默认随机选择一个非客户端区域的实例
    const otherZones = Array.from(instancesByZone.keys())
      .filter(zone => zone !== clientZone && instancesByZone.get(zone).length > 0);
    
    if (otherZones.length > 0) {
      const randomZone = otherZones[Math.floor(Math.random() * otherZones.length)];
      return this.selectInstanceInZone(instancesByZone.get(randomZone), context);
    }
    
    // 如果没有其他区域的实例，则随机选择一个实例
    return allInstances[Math.floor(Math.random() * allInstances.length)];
  }
  
  /**
   * 故障转移选择实例
   * @param allInstances 所有实例
   * @param instancesByZone 按区域分组的实例
   * @param clientZone 客户端区域
   * @param context 负载均衡上下文
   */
  private selectInstanceWithFailover(
    allInstances: ServiceInstanceEntity[],
    instancesByZone: Map<string, ServiceInstanceEntity[]>,
    clientZone: string,
    context: LoadBalancerContext,
  ): ServiceInstanceEntity {
    // 使用跨区域选择逻辑
    return this.selectInstanceAcrossZones(allInstances, instancesByZone, clientZone, context);
  }
  
  /**
   * 按区域分组实例
   * @param instances 实例列表
   */
  private groupInstancesByZone(
    instances: ServiceInstanceEntity[],
  ): Map<string, ServiceInstanceEntity[]> {
    const result = new Map<string, ServiceInstanceEntity[]>();
    
    for (const instance of instances) {
      const zone = instance.zone || 'default';
      
      if (!result.has(zone)) {
        result.set(zone, []);
      }
      
      result.get(zone).push(instance);
    }
    
    return result;
  }
  
  /**
   * 更新区域统计信息
   * @param serviceName 服务名称
   * @param instances 实例列表
   */
  private updateZoneStats(
    serviceName: string,
    instances: ServiceInstanceEntity[],
  ): void {
    // 获取服务的区域统计信息
    let serviceZoneStats = this.zoneStats.get(serviceName);
    
    if (!serviceZoneStats) {
      serviceZoneStats = new Map<string, ZoneStats>();
      this.zoneStats.set(serviceName, serviceZoneStats);
    }
    
    // 按区域分组实例
    const instancesByZone = this.groupInstancesByZone(instances);
    
    // 更新每个区域的统计信息
    for (const [zone, zoneInstances] of instancesByZone.entries()) {
      const healthyInstances = zoneInstances.filter(instance => instance.status === 'healthy');
      const averageLoad = zoneInstances.reduce((sum, instance) => sum + (instance.metadata?.load || 0), 0) / zoneInstances.length;
      
      serviceZoneStats.set(zone, {
        zone,
        instanceCount: zoneInstances.length,
        healthyInstanceCount: healthyInstances.length,
        averageLoad,
        lastUpdated: Date.now(),
      });
    }
  }
  
  /**
   * 检查是否需要区域故障转移
   * @param serviceName 服务名称
   * @param zone 区域
   */
  private shouldFailover(serviceName: string, zone: string): boolean {
    const serviceZoneStats = this.zoneStats.get(serviceName);
    
    if (!serviceZoneStats || !serviceZoneStats.has(zone)) {
      return false;
    }
    
    const zoneStats = serviceZoneStats.get(zone);
    const config = this.getZoneAwareConfig();
    
    // 如果健康实例比例低于阈值，则需要故障转移
    return zoneStats.healthyInstanceCount / zoneStats.instanceCount < config.failoverThreshold;
  }
  
  /**
   * 获取区域感知配置
   */
  private getZoneAwareConfig(): ZoneAwareConfig {
    return {
      ...this.defaultConfig,
      ...(this.config.options as ZoneAwareConfig),
    };
  }
  
  /**
   * 重置策略状态
   */
  override reset(): void {
    this.zoneStats.clear();
  }
}
